/**
 * Custom hook for localStorage operations with type safety and error handling
 * Provides a React-friendly interface for localStorage with automatic serialization
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * Hook return type definition
 */
export interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
  error: string | null;
}

/**
 * Options for the useLocalStorage hook
 */
export interface UseLocalStorageOptions<T> {
  serializer?: {
    parse: (value: string) => T;
    stringify: (value: T) => string;
  };
  onError?: (error: Error) => void;
}

/**
 * Default serializer using JSON
 */
const defaultSerializer = {
  parse: JSON.parse,
  stringify: JSON.stringify,
};

/**
 * Custom hook for localStorage with type safety
 * @param key The localStorage key
 * @param defaultValue Default value if key doesn't exist
 * @param options Configuration options
 * @returns Object containing value, setter, remover, and error state
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: UseLocalStorageOptions<T> = {}
): UseLocalStorageReturn<T> {
  const { serializer = defaultSerializer, onError } = options;
  
  const [value, setValue] = useState<T>(defaultValue);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handles errors consistently
   */
  const handleError = useCallback((error: Error, operation: string) => {
    const errorMessage = `localStorage ${operation} failed: ${error.message}`;
    console.error(errorMessage, error);
    setError(errorMessage);
    onError?.(error);
  }, [onError]);

  /**
   * Reads value from localStorage
   */
  const readValue = useCallback((): T => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return defaultValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      
      return serializer.parse(item);
    } catch (error) {
      handleError(error as Error, 'read');
      return defaultValue;
    }
  }, [key, defaultValue, serializer, handleError]);

  /**
   * Writes value to localStorage
   */
  const writeValue = useCallback((valueToStore: T) => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('localStorage is not available in this environment');
      return;
    }

    try {
      const serializedValue = serializer.stringify(valueToStore);
      window.localStorage.setItem(key, serializedValue);
      setError(null); // Clear any previous errors
    } catch (error) {
      handleError(error as Error, 'write');
    }
  }, [key, serializer, handleError]);

  /**
   * Removes value from localStorage
   */
  const removeValue = useCallback(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('localStorage is not available in this environment');
      return;
    }

    try {
      window.localStorage.removeItem(key);
      setValue(defaultValue);
      setError(null); // Clear any previous errors
    } catch (error) {
      handleError(error as Error, 'remove');
    }
  }, [key, defaultValue, handleError]);

  /**
   * Updates the value state and localStorage
   */
  const setStoredValue = useCallback((newValue: T | ((prev: T) => T)) => {
    try {
      const valueToStore = newValue instanceof Function ? newValue(value) : newValue;
      setValue(valueToStore);
      writeValue(valueToStore);
    } catch (error) {
      handleError(error as Error, 'update');
    }
  }, [value, writeValue, handleError]);

  /**
   * Initialize value from localStorage on mount
   */
  useEffect(() => {
    const initialValue = readValue();
    setValue(initialValue);
  }, [readValue]);

  /**
   * Listen for localStorage changes from other tabs/windows
   */
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const newValue = serializer.parse(e.newValue);
          setValue(newValue);
          setError(null);
        } catch (error) {
          handleError(error as Error, 'sync');
        }
      } else if (e.key === key && e.newValue === null) {
        // Key was removed
        setValue(defaultValue);
        setError(null);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key, defaultValue, serializer, handleError]);

  return {
    value,
    setValue: setStoredValue,
    removeValue,
    error,
  };
}

/**
 * Specialized hook for storing view preferences
 */
export function useViewPreference() {
  return useLocalStorage<'grid' | 'list'>('newsViewMode', 'grid');
}

/**
 * Type exports for use in other modules
 */
export type { UseLocalStorageReturn, UseLocalStorageOptions };
