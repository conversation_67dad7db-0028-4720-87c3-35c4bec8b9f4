"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { NewsActions } from "@/components/news-actions";
import { Clock, User, ExternalLink } from "lucide-react";
import { NewsItem } from "@/types/news";
import { toast } from "sonner";

interface NewsCardProps {
  newsItem: NewsItem;
  newsKey: string;
  onDelete: (key: string) => void;
}

export function NewsCard({ newsItem, newsKey, onDelete }: NewsCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const firebaseUrl = process.env.NEXT_PUBLIC_FIREBASE_DB_URL || "https://debug-cnews-default-rtdb.firebaseio.com";
      const response = await fetch(
        `${firebaseUrl}/news/${newsKey}.json`,
        { method: "DELETE" }
      );

      if (response.ok) {
        onDelete(newsKey);
        toast.success("Notícia removida com sucesso!", {
          description: "A notícia foi permanentemente excluída do sistema."
        });
      } else {
        throw new Error("Erro ao remover notícia");
      }
    } catch (error) {
      console.error("Erro ao deletar:", error);
      toast.error("Erro ao remover notícia", {
        description: "Tente novamente ou contate o suporte."
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSend = async () => {
    if (!newsItem.newsUrl) {
      toast.error("URL da notícia não encontrada", {
        description: "Não é possível processar uma notícia sem URL."
      });
      return;
    }

    setIsSending(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_CNEWS_API_URL;
      
      if (!apiUrl) {
        throw new Error("URL da API não configurada");
      }

      // Add timeout to fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify({ url: newsItem.newsUrl }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text().catch(() => "");
        throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ` - ${errorText}` : ""}`);
      }

      toast.success("Notícia enviada com sucesso!", {
        description: "A notícia foi processada e está sendo analisada."
      });
    } catch (error) {
      console.error("Erro ao enviar:", error);
      
      let errorMessage = "Erro ao enviar notícia";
      let errorDescription = "Verifique sua conexão e tente novamente.";

      if (error instanceof Error) {
        if (error.name === "AbortError") {
          errorMessage = "Timeout na requisição";
          errorDescription = "A requisição demorou muito para responder. Tente novamente.";
        } else if (error.message.includes("Failed to fetch")) {
          errorMessage = "Erro de conexão";
          errorDescription = "Não foi possível conectar com o servidor. Verifique sua conexão com a internet.";
        } else if (error.message.includes("URL da API não configurada")) {
          errorMessage = "Configuração inválida";
          errorDescription = "A URL da API não está configurada corretamente.";
        } else if (error.message.includes("HTTP")) {
          errorMessage = "Erro do servidor";
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription
      });
    } finally {
      setIsSending(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) return "Hoje";
      if (diffDays === 2) return "Ontem";
      if (diffDays <= 7) return `${diffDays - 1} dias atrás`;
      
      return date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 bg-white border border-gray-200">
      {/* Header com Autor */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10 ring-2 ring-gray-100">
              <AvatarImage 
                src={newsItem.newsAuthorImg} 
                alt={newsItem.newsAuthor}
                onLoad={() => setImageLoaded(true)}
              />
              <AvatarFallback className="bg-linear-to-br from-blue-500 to-purple-600 text-white font-semibold">
                {newsItem.newsAuthor.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-2">
                <User className="h-3 w-3 text-gray-400" />
                <p className="font-semibold text-gray-900 text-sm truncate">
                  {newsItem.newsAuthor}
                </p>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <Clock className="h-3 w-3 text-gray-400" />
                <p className="text-xs text-gray-500">
                  {formatDate(newsItem.newsDate)}
                </p>
              </div>
            </div>
          </div>
          
          {newsItem.selected && (
            <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
              ✓ Selecionado
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Título */}
        {newsItem.newsTitle && (
          <h3 className="font-bold text-lg leading-tight text-gray-900 line-clamp-2 group-hover:text-blue-700 transition-colors">
            {newsItem.newsTitle}
          </h3>
        )}

        {/* Imagem */}
        {newsItem.newsImg && !imageError && (
          <div className="relative overflow-hidden rounded-lg bg-gray-100">
            <div className={`aspect-video transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}>
              <img
                src={newsItem.newsImg}
                alt={newsItem.newsTitle || "Notícia"}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
            </div>
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              </div>
            )}
          </div>
        )}

        {/* Conteúdo */}
        <div className="space-y-3">
          <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
            {newsItem.newsContent}
          </p>
          
          {newsItem.newsUrl && (
            <a
              href={newsItem.newsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors"
            >
              <ExternalLink className="h-3 w-3" />
              <span>Ler artigo completo</span>
            </a>
          )}
        </div>

        {/* Ações */}
        <div className="flex justify-end pt-4 border-t border-gray-100">
          <NewsActions
            newsKey={newsKey}
            newsUrl={newsItem.newsUrl}
            onDelete={handleDelete}
            onSend={handleSend}
            isDeleting={isDeleting}
            isSending={isSending}
          />
        </div>
      </CardContent>
    </Card>
  );
}