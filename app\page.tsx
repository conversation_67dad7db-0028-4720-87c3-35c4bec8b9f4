"use client";

import { useState, useEffect, useMemo } from "react";
import { NewsCard } from "@/components/news-card";
import { NewsListItem } from "@/components/news-list-item";
import { NewsSkeleton } from "@/components/news-skeleton";
import { NewsListSkeleton } from "@/components/news-list-skeleton";
import { ErrorState } from "@/components/error-state";
import { SearchFilters } from "@/components/search-filters";
import { NewsStats } from "@/components/news-stats";
import { ViewToggle } from "@/components/view-toggle";
import { NewsData, NewsItem } from "@/types/news";
import { Newspaper, RefreshCw, Loader2, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

export default function Home() {
  const [newsData, setNewsData] = useState<NewsData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resetting, setResetting] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Estados de filtros
  const [filters, setFilters] = useState({
    search: "",
    author: "",
    date: undefined as Date | undefined,
    sort: "date-desc",
  });

  // Carregar preferência de visualização do localStorage
  useEffect(() => {
    const savedView = localStorage.getItem("newsViewMode") as
      | "grid"
      | "list"
      | null;
    if (savedView) {
      setViewMode(savedView);
    }
  }, []);

  // Salvar preferência de visualização no localStorage
  const handleViewChange = (view: "grid" | "list") => {
    setViewMode(view);
    localStorage.setItem("newsViewMode", view);
  };

  const fetchNews = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        "https://debug-cnews-default-rtdb.firebaseio.com/news.json"
      );

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      setNewsData(data || {});

      if (data && Object.keys(data).length > 0) {
        toast.success(
          `${Object.keys(data).length} notícias carregadas com sucesso!`
        );
      }
    } catch (error) {
      console.error("Erro ao buscar notícias:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Erro desconhecido ao carregar notícias";
      setError(errorMessage);
      toast.error("Erro ao carregar notícias", {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const resetAllNews = async () => {
    setResetting(true);

    try {
      const response = await fetch(
        "https://debug-cnews-default-rtdb.firebaseio.com/news.json",
        { method: "DELETE" }
      );

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      // Limpar o estado local
      setNewsData({});

      // Limpar filtros também
      setFilters({
        search: "",
        author: "",
        date: undefined,
        sort: "date-desc",
      });

      toast.success("Todas as notícias foram removidas com sucesso!", {
        description: "O banco de dados foi resetado completamente.",
      });
    } catch (error) {
      console.error("Erro ao resetar notícias:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Erro desconhecido ao resetar notícias";

      toast.error("Erro ao resetar notícias", {
        description: errorMessage,
      });
    } finally {
      setResetting(false);
    }
  };

  useEffect(() => {
    fetchNews();
  }, []);

  const handleDeleteNews = (deletedKey: string) => {
    setNewsData((prevData) => {
      const newData = { ...prevData };
      delete newData[deletedKey];
      return newData;
    });
  };

  // Filtrar e ordenar notícias
  const filteredNews = useMemo(() => {
    let newsArray = Object.entries(newsData);

    // Filtro de busca
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      newsArray = newsArray.filter(
        ([_, item]) =>
          item.newsTitle?.toLowerCase().includes(searchLower) ||
          item.newsContent?.toLowerCase().includes(searchLower) ||
          item.newsAuthor?.toLowerCase().includes(searchLower)
      );
    }

    // Filtro por autor
    if (filters.author) {
      newsArray = newsArray.filter(
        ([_, item]) => item.newsAuthor === filters.author
      );
    }

    // Filtro por data
    if (filters.date) {
      const filterDate = filters.date.toDateString();
      newsArray = newsArray.filter(([_, item]) => {
        const itemDate = new Date(item.newsDate).toDateString();
        return itemDate === filterDate;
      });
    }

    // Ordenação
    newsArray.sort(([, a], [, b]) => {
      switch (filters.sort) {
        case "date-asc":
          return (
            new Date(a.newsDate).getTime() - new Date(b.newsDate).getTime()
          );
        case "date-desc":
          return (
            new Date(b.newsDate).getTime() - new Date(a.newsDate).getTime()
          );
        case "author":
          return a.newsAuthor.localeCompare(b.newsAuthor);
        case "title":
          return (a.newsTitle || "").localeCompare(b.newsTitle || "");
        default:
          return 0;
      }
    });

    return newsArray;
  }, [newsData, filters]);

  // Obter lista de autores únicos
  const uniqueAuthors = useMemo(() => {
    const authors = Object.values(newsData).map((item) => item.newsAuthor);
    return Array.from(new Set(authors)).sort();
  }, [newsData]);

  const clearFilters = () => {
    setFilters({
      search: "",
      author: "",
      date: undefined,
      sort: "date-desc",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-linear-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4 py-8">
          {/* Header com loading */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Newspaper className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">News Admin</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-gray-600">Carregando notícias...</span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="bg-white p-4 rounded-lg border animate-pulse"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg" />
                  <div className="space-y-2 flex-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4" />
                    <div className="h-6 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Content skeleton baseado no modo de visualização */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <NewsSkeleton key={index} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <NewsListSkeleton key={index} />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-linear-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Newspaper className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">News Admin</h1>
            </div>
          </div>

          <ErrorState message={error} onRetry={fetchNews} />
        </div>
      </div>
    );
  }

  const totalNews = Object.keys(newsData).length;
  const filteredCount = filteredNews.length;

  return (
    <div className="min-h-screen bg-linear-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 rounded-lg shadow-lg">
              <Newspaper className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">News Admin</h1>
              <p className="text-gray-600 mt-1">
                {totalNews === 0
                  ? "Nenhuma notícia encontrada"
                  : filteredCount !== totalNews
                  ? `${filteredCount} de ${totalNews} notícias`
                  : `${totalNews} notícia${
                      totalNews > 1 ? "s" : ""
                    } encontrada${totalNews > 1 ? "s" : ""}`}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Toggle de visualização */}
            {totalNews > 0 && (
              <ViewToggle view={viewMode} onViewChange={handleViewChange} />
            )}

            <Button
              onClick={fetchNews}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 shadow-xs hover:shadow-md transition-shadow"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Atualizar</span>
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={resetting || totalNews === 0}
                  className="flex items-center space-x-2 shadow-xs hover:shadow-md transition-shadow text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                >
                  {resetting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Resetando...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4" />
                      <span>Resetar</span>
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>

              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmar reset completo</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta ação irá{" "}
                    <strong>
                      remover permanentemente todas as {totalNews} notícias
                    </strong>{" "}
                    do banco de dados. Esta operação não pode ser desfeita.
                    <br />
                    <br />
                    Tem certeza que deseja continuar?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={resetAllNews}
                    disabled={resetting}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {resetting ? "Resetando..." : "Sim, resetar tudo"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {totalNews > 0 && (
          <>
            {/* Estatísticas */}
            <NewsStats newsData={newsData} />

            {/* Filtros de Busca */}
            <SearchFilters
              onSearch={(query) =>
                setFilters((prev) => ({ ...prev, search: query }))
              }
              onFilterByAuthor={(author) =>
                setFilters((prev) => ({ ...prev, author }))
              }
              onFilterByDate={(date) =>
                setFilters((prev) => ({ ...prev, date }))
              }
              onSortChange={(sort) => setFilters((prev) => ({ ...prev, sort }))}
              onClearFilters={clearFilters}
              authors={uniqueAuthors}
              activeFilters={filters}
            />
          </>
        )}

        {/* Conteúdo Principal */}
        {totalNews === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
            <div className="text-center">
              <div className="p-4 bg-gray-100 rounded-full mb-4 inline-block">
                <Newspaper className="h-16 w-16 text-gray-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhuma notícia encontrada
              </h2>
              <p className="text-gray-600 mb-4 max-w-md">
                Não há notícias disponíveis no momento. Tente atualizar a página
                ou entre em contato com o suporte.
              </p>
              <Button
                onClick={fetchNews}
                variant="outline"
                className="shadow-xs hover:shadow-md transition-shadow"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar novamente
              </Button>
            </div>
          </div>
        ) : filteredNews.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
            <div className="text-center">
              <div className="p-4 bg-gray-100 rounded-full mb-4 inline-block">
                <Newspaper className="h-16 w-16 text-gray-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum resultado encontrado
              </h2>
              <p className="text-gray-600 mb-4 max-w-md">
                Nenhuma notícia corresponde aos filtros aplicados. Tente ajustar
                os critérios de busca.
              </p>
              <Button
                onClick={clearFilters}
                variant="outline"
                className="shadow-xs hover:shadow-md transition-shadow"
              >
                Limpar filtros
              </Button>
            </div>
          </div>
        ) : (
          <div className="mt-6">
            {viewMode === "grid" ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredNews.map(([key, newsItem]) => (
                  <NewsCard
                    key={key}
                    newsItem={newsItem}
                    newsKey={key}
                    onDelete={handleDeleteNews}
                  />
                ))}
              </div>
            ) : (
              <div className="space-y-4 max-w-6xl">
                {filteredNews.map(([key, newsItem]) => (
                  <NewsListItem
                    key={key}
                    newsItem={newsItem}
                    newsKey={key}
                    onDelete={handleDeleteNews}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
